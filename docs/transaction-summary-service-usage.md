# TransactionSummaryReportService Usage Guide

## Overview

The `TransactionSummaryReportService` provides functionality to generate and save transaction summary details to the database. It reads transaction data from the `transaction_e_pos` table and creates summary records **only** in the `transaction_summary_report_detail` table (no main report table).

## Key Features

- ✅ **Insert transaction summary details** directly into `transaction_summary_report_detail` table
- ✅ **Generate summary details** from uploaded transaction data in `transaction_e_pos`
- ✅ **Automatic financial calculations** (MDR, VAT, withholding tax, fees)
- ✅ **Merchant grouping** and aggregation
- ✅ **Database transaction support** with rollback on errors
- ✅ **Sample data creation** for testing
- ✅ **No main report table dependency** - works with detail table only

## Basic Usage

### 1. Initialize the Service

```typescript
import { TransactionSummaryReportService } from '../src/main/services/transactionSummaryReportService';

const summaryReportService = new TransactionSummaryReportService();
```

### 2. Insert Transaction Summary Details

```typescript
// Create sample transaction summary data
const sampleData = summaryReportService.createSampleTransactionSummaryData(
  'TEST123456789',           // Merchant VAT
  'Test Merchant Co., Ltd.', // Merchant Name
  10,                        // Transaction Count
  1000.00                    // Total Amount
);

// Insert into database
const batchId = `BATCH_${Date.now()}`;
const result = await summaryReportService.insertTransactionSummaryDetails(
  [sampleData],
  batchId,
  'USER_NAME'
);

if (result.success) {
  console.log(`✅ Inserted ${result.insertedCount} records`);
} else {
  console.error(`❌ Error: ${result.error}`);
}
```

### 3. Generate Summary from Uploaded Transactions

```typescript
// This method reads from transaction_e_pos and creates summary records in detail table only
const reportResult = await summaryReportService.generateSummaryFromUploadedTransactions(
  batchId,
  ['file1.xlsx', 'file2.xlsx'], // Processed file names
  'USER_NAME'
);

if (reportResult.success) {
  console.log(`✅ Summary details saved successfully`);
  console.log(`📊 Summary details: ${reportResult.summaryDetails?.length}`);
}
```

## Method Reference

### `insertTransactionSummaryDetails(summaryDetails, batchId, createdBy)`

Directly inserts transaction summary data into the `transaction_summary_report_detail` table.

**Parameters:**
- `summaryDetails`: Array of `MerchantSummaryData` objects
- `batchId`: Unique identifier for the batch
- `createdBy`: Username of the user creating the records

**Returns:**
```typescript
{
  success: boolean;
  insertedCount: number;
  error?: string;
}
```

### `generateSummaryFromUploadedTransactions(batchId, processedFiles, createdBy)`

Generates transaction summary from data in `transaction_e_pos` table and saves to detail table only.

**Parameters:**
- `batchId`: Unique identifier for the batch
- `processedFiles`: Array of processed file names
- `createdBy`: Username of the user creating the records

**Returns:**
```typescript
{
  success: boolean;
  summaryDetails?: MerchantSummaryData[];
  error?: string;
}
```

### `createSampleTransactionSummaryData(merchantVat, merchantName, transactionCount, totalAmount)`

Creates sample transaction summary data for testing purposes.

**Parameters:**
- `merchantVat`: Merchant VAT number (default: 'TEST123456789')
- `merchantName`: Merchant name (default: 'Test Merchant Co., Ltd.')
- `transactionCount`: Number of transactions (default: 10)
- `totalAmount`: Total transaction amount (default: 1000.00)

**Returns:** `MerchantSummaryData` object with calculated financial values

## Data Structure

### MerchantSummaryData Interface

```typescript
interface MerchantSummaryData {
  merchantVat: string;
  merchantName: string;
  transactionDate: string;
  channelType: string;
  transactionCount: number;
  totalAmount: number;
  mdrRate: number;
  mdrAmount: number;
  vatPercentage: number;
  vatAmount: number;
  netAmount: number;
  withholdingTaxRate: number;
  withholdTax: number;
  transferFee: number;
  reimbursementFee: number;
  serviceFee: number;
  finalNetAmount: number;
  cupBusinessTaxFee: number;
  isTransfer: number; // 0 = not transferred, 1 = transferred
  tradeStatus: 'SUCCESS' | 'REFUND';
  bankCode: string;
  bankNameTh: string;
  bankNameEn: string;
}
```

## Database Tables

### transaction_summary_report_detail

The service inserts data **only** into this table (no main report table). Key fields include:

- `merchant_vat`: Merchant VAT number
- `merchant_name`: Merchant name
- `transaction_date`: Date of the transactions
- `channel_type`: Payment channel type (e.g., WeChat, UNIPAY)
- `transaction_count`: Number of transactions
- `total_amount`: Total transaction amount
- `mdr_amount`: MDR fee amount
- `vat_amount`: VAT amount
- `final_net_amount`: Final net amount after all deductions
- `is_transfer`: Transfer status (0/1)
- `create_by`: User who created the record
- `create_dt`: Creation timestamp
- `update_by`: User who last updated the record
- `update_dt`: Last update timestamp

**Note:** The service stores only essential transaction summary data. Batch tracking columns have been removed for simplicity.

## Integration with Transaction Handler

The service is already integrated into the transaction processing workflow in `transactionHandler.ts`:

```typescript
const summaryReportService = new TransactionSummaryReportService();

// After processing transaction files
const reportResult = await summaryReportService.generateSummaryFromUploadedTransactions(
  batchId,
  processedFileNames,
  results.processedFiles,
  results.savedTransactions,
  currentUser
);
```

## Error Handling

The service includes comprehensive error handling:

- Database transaction rollback on errors
- Detailed error messages
- Graceful failure without affecting main transaction processing
- Logging of all operations

## Examples

See `examples/transaction-summary-service-example.ts` for complete usage examples including:

- Basic insertion of summary details
- Batch processing multiple merchants
- Custom data creation
- Error handling patterns

## Notes

- All financial calculations are rounded to 2 decimal places
- The service supports both SUCCESS and REFUND transaction types
- Database operations use transactions for data consistency
- The service integrates with existing merchant, bank, and network service tables when available
