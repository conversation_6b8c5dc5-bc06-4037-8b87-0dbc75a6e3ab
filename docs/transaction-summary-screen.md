# Transaction Summary Screen

## Overview
The Transaction Summary screen provides a comprehensive view of transaction data with both summary and detailed views. It uses **only the `transaction_summary_report_detail` table** for all data operations, eliminating dependency on the main report table.

## Features

### 1. Header Controls
- **Search**: Refresh and load transaction data from detail table
- **Cancel**: Reset form to default values
- **Approve**: Mark selected transactions as transferred (requires create permission)
- **Close**: Navigate back to previous screen
- **Print**: Open print dialog for current view
- **Export**: Export current data as CSV file

### 2. Filter Controls
- **Print Type**: Toggle between "Transferred" and "Pending" views
- **Date**: Date picker for filtering transactions by transaction_date
- **Merchant VAT**: Filter by specific merchant VAT number
- **Channel Type**: Filter by payment channel (WeChat, UNIPAY, etc.)

### 3. Summary Section
Displays key financial metrics calculated from detail table:
- **Transferred Today**: Total final_net_amount where is_transfer = 1
- **Pending Transfer**: Total final_net_amount where is_transfer = 0 (highlighted in red)
- **Total Amount**: Grand total of all final_net_amount (prominently displayed in black box)

### 4. Tab Navigation
- **Summary**: Shows aggregated view with merchant summaries
- **Details**: Shows individual transaction summary records
- **Transfer Status**: Shows transfer management interface

### 5. Summary Table (Summary Tab)
Columns from transaction_summary_report_detail:
- Merchant VAT: merchant_vat
- Merchant Name: merchant_name
- Channel: channel_type
- Transaction Count: transaction_count
- Total Amount: total_amount (formatted as Thai currency)
- MDR Amount: mdr_amount
- VAT Amount: vat_amount
- Final Net Amount: final_net_amount
- Transfer Status: is_transfer (0=Pending, 1=Transferred)

### 6. Transaction Details Table (Details Tab)
All columns from transaction_summary_report_detail:
- Transaction Date: transaction_date
- Merchant VAT: merchant_vat
- Merchant Name: merchant_name
- Channel Type: channel_type
- Transaction Count: transaction_count
- Total Amount: total_amount
- MDR Rate: mdr_rate (%)
- MDR Amount: mdr_amount
- VAT Percentage: vat_percentage (%)
- VAT Amount: vat_amount
- Net Amount: net_amount
- Withholding Tax Rate: withholding_tax_rate (%)
- Withholding Tax: withhold_tax
- Transfer Fee: transfer_fee
- Reimbursement Fee: reimbursement_fee
- Service Fee: service_fee
- Final Net Amount: final_net_amount
- CUP Business Tax Fee: cup_business_tax_fee
- Transfer Status: is_transfer

## Technical Implementation

### Data Types
```typescript
// Main interface matching transaction_summary_report_detail table
interface TransactionSummaryDetailItem {
  id: number;
  merchant_vat: string;
  merchant_name: string;
  transaction_date: string;
  channel_type: string;
  transaction_count: number;
  total_amount: number;
  mdr_rate: number;
  mdr_amount: number;
  vat_percentage: number;
  vat_amount: number;
  net_amount: number;
  withholding_tax_rate: number;
  withhold_tax: number;
  transfer_fee: number;
  reimbursement_fee: number;
  service_fee: number;
  final_net_amount: number;
  cup_business_tax_fee: number;
  is_transfer: number; // 0 = not transferred, 1 = transferred
  create_by: string;
  create_dt: string;
  update_by: string;
  update_dt: string;
}

// Aggregated summary for display
interface TransactionSummaryData {
  totalTransferred: number;
  totalPending: number;
  totalAmount: number;
  recordCount: number;
  merchantCount: number;
  channelBreakdown: { [channel: string]: number };
}

// Filter interface
interface TransactionSummaryFilters {
  startDate?: string;
  endDate?: string;
  merchantVat?: string;
  channelType?: string;
  isTransfer?: number; // 0, 1, or undefined for all
  page?: number;
  pageSize?: number;
}
```

### Database Operations
All operations use only the `transaction_summary_report_detail` table:

```sql
-- Get summary data with filters
SELECT
  merchant_vat,
  merchant_name,
  transaction_date,
  channel_type,
  SUM(transaction_count) as total_transactions,
  SUM(total_amount) as total_amount,
  SUM(final_net_amount) as final_net_amount,
  is_transfer
FROM transaction_summary_report_detail
WHERE transaction_date BETWEEN $1 AND $2
  AND ($3 IS NULL OR merchant_vat = $3)
  AND ($4 IS NULL OR channel_type = $4)
  AND ($5 IS NULL OR is_transfer = $5)
GROUP BY merchant_vat, merchant_name, transaction_date, channel_type, is_transfer
ORDER BY transaction_date DESC, merchant_vat;

-- Update transfer status
UPDATE transaction_summary_report_detail
SET is_transfer = $1, update_by = $2, update_dt = CURRENT_TIMESTAMP
WHERE id = $3;

-- Get aggregated totals
SELECT
  SUM(CASE WHEN is_transfer = 1 THEN final_net_amount ELSE 0 END) as total_transferred,
  SUM(CASE WHEN is_transfer = 0 THEN final_net_amount ELSE 0 END) as total_pending,
  SUM(final_net_amount) as total_amount,
  COUNT(*) as record_count,
  COUNT(DISTINCT merchant_vat) as merchant_count
FROM transaction_summary_report_detail
WHERE transaction_date BETWEEN $1 AND $2;
```

### Key Features
- **Single Table Architecture**: Uses only `transaction_summary_report_detail` table
- **Role-based Access Control**: Uses `RoleBasedComponent` for permission-based UI
- **Responsive Design**: Tables are horizontally scrollable on smaller screens
- **Real Data Integration**: Direct database queries without mock data
- **Export Functionality**: CSV export for both summary and detail views
- **Print Support**: Browser print functionality
- **Thai Language Support**: Proper formatting for Thai text and currency
- **Transfer Management**: Update transfer status directly in detail table

### API Endpoints
- `get-transaction-summary-details`: Get filtered detail records
- `get-transaction-summary-aggregates`: Get calculated totals
- `update-transfer-status`: Update is_transfer field
- `export-transaction-summary`: Export data as CSV

### Styling
- Uses Tailwind CSS for consistent styling
- Blue header matching Windows application theme
- Alternating row colors for better readability
- Proper spacing and borders for table elements
- Responsive design with minimum column widths

### Navigation
- Accessible via `/transaction-summary` route
- Added to sidebar navigation with 📊 icon
- Protected route requiring authentication

## Implementation Benefits
1. **Simplified Architecture**: No dependency on main report table
2. **Better Performance**: Direct queries on detail table with proper indexes
3. **Real-time Data**: Always shows current state without report generation
4. **Flexible Filtering**: Multiple filter options on detail fields
5. **Transfer Management**: Direct control over transfer status

## Usage
1. Navigate to Transaction Summary from the sidebar
2. Select desired date range and filters
3. Click Search to load data from detail table
4. Switch between Summary, Details, and Transfer Status tabs
5. Use Export to download data as CSV
6. Use Print for hard copy reports
7. Update transfer status for individual records (if authorized)

## Permissions
- **Read Access**: View transaction summaries and details
- **Create Permission**: Required for transfer status updates
- **Role-based UI**: Different interface elements based on user role
