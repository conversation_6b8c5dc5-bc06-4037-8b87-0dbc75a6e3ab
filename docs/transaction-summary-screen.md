# Transaction Summary Screen

## Overview
The Transaction Summary screen provides a streamlined workflow for managing transaction data with three focused tabs. It uses **only the `transaction_summary_report_detail` table** for all data operations, providing real-time access to transaction information.

## New Workflow Features

### 1. Header Controls
- **Refresh All**: Refresh data for all three tabs simultaneously
- **Search Filtered**: Apply current filter criteria to the filtered transactions tab
- **Clear Filters**: Reset all filter criteria to default values
- **Export Current Tab**: Export data from the currently active tab as CSV
- **Print**: Open print dialog for current view

### 2. Filter Controls (for Filtered Tab only)
- **Start Date**: Beginning date for date range filtering
- **End Date**: Ending date for date range filtering
- **Merchant VAT**: Filter by specific merchant VAT number
- **Channel Type**: Filter by payment channel (WeChat, UNIPAY, etc.)
- **Transfer Status**: Filter by transfer status (All, Pending, Transferred)

### 3. Summary Section
Displays real-time counts and totals for each tab:
- **Today's Transactions**: Count and total amount for current date
- **Pending Transactions**: Count and total amount for all pending transfers
- **Filtered Transactions**: Count and total amount based on filter criteria

### 4. Three-Tab Navigation
- **📅 List Transaction Today**: Shows all transactions for today's date
- **⏳ List Transaction Pending**: Shows all pending transactions across all dates
- **🔍 List Transaction Follow Filter**: Shows transactions based on filter criteria

### 5. Today's Transactions Table (📅 List Transaction Today)
Shows all transactions for the current date only:
- Transaction Date: transaction_date (current date)
- Merchant VAT: merchant_vat
- Merchant Name: merchant_name
- Channel Type: channel_type
- Transaction Count: transaction_count
- Total Amount: total_amount (formatted as Thai currency)
- Final Net Amount: final_net_amount
- Transfer Status: is_transfer (0=Pending, 1=Transferred)
- Actions: Mark Transferred/Pending (role-based)

### 6. Pending Transactions Table (⏳ List Transaction Pending)
Shows all pending transactions across all dates:
- Transaction Date: transaction_date
- Merchant VAT: merchant_vat
- Merchant Name: merchant_name
- Channel Type: channel_type
- Transaction Count: transaction_count
- Total Amount: total_amount
- Final Net Amount: final_net_amount
- Actions: Mark as Transferred (role-based)

### 7. Filtered Transactions Table (🔍 List Transaction Follow Filter)
Shows transactions based on filter criteria:
- Transaction Date: transaction_date
- Merchant VAT: merchant_vat
- Merchant Name: merchant_name
- Channel Type: channel_type
- Transaction Count: transaction_count
- Total Amount: total_amount
- Final Net Amount: final_net_amount
- Transfer Status: is_transfer (0=Pending, 1=Transferred)
- Actions: Mark Transferred/Pending (role-based)

## Technical Implementation

### Data Types
```typescript
// Main interface matching transaction_summary_report_detail table
interface TransactionSummaryDetailItem {
  id: number;
  merchant_vat: string;
  merchant_name: string;
  transaction_date: string;
  channel_type: string;
  transaction_count: number;
  total_amount: number;
  mdr_rate: number;
  mdr_amount: number;
  vat_percentage: number;
  vat_amount: number;
  net_amount: number;
  withholding_tax_rate: number;
  withhold_tax: number;
  transfer_fee: number;
  reimbursement_fee: number;
  service_fee: number;
  final_net_amount: number;
  cup_business_tax_fee: number;
  is_transfer: number; // 0 = not transferred, 1 = transferred
  create_by: string;
  create_dt: string;
  update_by: string;
  update_dt: string;
}

// Aggregated summary for display
interface TransactionSummaryData {
  totalTransferred: number;
  totalPending: number;
  totalAmount: number;
  recordCount: number;
  merchantCount: number;
  channelBreakdown: { [channel: string]: number };
}

// Filter interface
interface TransactionSummaryFilters {
  startDate?: string;
  endDate?: string;
  merchantVat?: string;
  channelType?: string;
  isTransfer?: number; // 0, 1, or undefined for all
  page?: number;
  pageSize?: number;
}
```

### Database Operations
All operations use only the `transaction_summary_report_detail` table:

```sql
-- Get summary data with filters
SELECT
  merchant_vat,
  merchant_name,
  transaction_date,
  channel_type,
  SUM(transaction_count) as total_transactions,
  SUM(total_amount) as total_amount,
  SUM(final_net_amount) as final_net_amount,
  is_transfer
FROM transaction_summary_report_detail
WHERE transaction_date BETWEEN $1 AND $2
  AND ($3 IS NULL OR merchant_vat = $3)
  AND ($4 IS NULL OR channel_type = $4)
  AND ($5 IS NULL OR is_transfer = $5)
GROUP BY merchant_vat, merchant_name, transaction_date, channel_type, is_transfer
ORDER BY transaction_date DESC, merchant_vat;

-- Update transfer status
UPDATE transaction_summary_report_detail
SET is_transfer = $1, update_by = $2, update_dt = CURRENT_TIMESTAMP
WHERE id = $3;

-- Get aggregated totals
SELECT
  SUM(CASE WHEN is_transfer = 1 THEN final_net_amount ELSE 0 END) as total_transferred,
  SUM(CASE WHEN is_transfer = 0 THEN final_net_amount ELSE 0 END) as total_pending,
  SUM(final_net_amount) as total_amount,
  COUNT(*) as record_count,
  COUNT(DISTINCT merchant_vat) as merchant_count
FROM transaction_summary_report_detail
WHERE transaction_date BETWEEN $1 AND $2;
```

### Key Features
- **Single Table Architecture**: Uses only `transaction_summary_report_detail` table
- **Role-based Access Control**: Uses `RoleBasedComponent` for permission-based UI
- **Responsive Design**: Tables are horizontally scrollable on smaller screens
- **Real Data Integration**: Direct database queries without mock data
- **Export Functionality**: CSV export for both summary and detail views
- **Print Support**: Browser print functionality
- **Thai Language Support**: Proper formatting for Thai text and currency
- **Transfer Management**: Update transfer status directly in detail table
- **Multiple Tab Views**: Six different views for comprehensive data analysis
- **Filter-Independent Tabs**: Transfer Today and Pending Transfer tabs ignore all filters
- **Real-time Counters**: Tab badges show record counts for quick reference
- **Color-coded Interface**: Different colors for different transfer statuses

### API Endpoints
- `get-transaction-summary-details`: Get filtered detail records
- `get-transaction-summary-aggregates`: Get calculated totals
- `update-transfer-status`: Update is_transfer field
- `export-transaction-summary`: Export data as CSV

### Styling
- Uses Tailwind CSS for consistent styling
- Blue header matching Windows application theme
- Alternating row colors for better readability
- Proper spacing and borders for table elements
- Responsive design with minimum column widths

### Navigation
- Accessible via `/transaction-summary` route
- Added to sidebar navigation with 📊 icon
- Protected route requiring authentication

## Implementation Benefits
1. **Simplified Architecture**: No dependency on main report table
2. **Better Performance**: Direct queries on detail table with proper indexes
3. **Real-time Data**: Always shows current state without report generation
4. **Flexible Filtering**: Multiple filter options on detail fields
5. **Transfer Management**: Direct control over transfer status

## Usage
1. Navigate to Transaction Summary from the sidebar
2. **Today's Transactions**: Automatically loads current date transactions
3. **Pending Transactions**: Shows all pending transfers across all dates
4. **Filtered Transactions**:
   - Set filter criteria (date range, merchant VAT, channel, transfer status)
   - Click "Search Filtered" to apply filters
5. Use "Refresh All" to update all tabs simultaneously
6. Use "Export Current Tab" to download active tab data as CSV
7. Use "Clear Filters" to reset filter criteria
8. Update transfer status for individual records (if authorized)
9. Use Print for hard copy reports

### New Workflow Benefits
- **Simplified Interface**: Only three focused tabs instead of six
- **Real-time Data**: Automatic loading of today's and pending transactions
- **Flexible Filtering**: Comprehensive filter options for the filtered tab
- **Efficient Navigation**: Clear purpose for each tab with visual indicators

## Permissions
- **Read Access**: View transaction summaries and details
- **Create Permission**: Required for transfer status updates
- **Role-based UI**: Different interface elements based on user role
