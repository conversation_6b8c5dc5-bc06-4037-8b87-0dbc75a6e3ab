import { useState, useEffect } from 'react';
import { Button } from '../components/button';
import { useAuth } from '../contexts/AuthContext';
import { useNotification } from '../contexts/NotificationContext';
import { safeIpcInvoke } from '../utils/electron';
import { RoleBasedComponent } from '../components/RoleBasedComponent';

// Types for Transaction Summary using only detail table
interface TransactionSummaryDetailItem {
  id: number;
  merchant_vat: string;
  merchant_name: string;
  transaction_date: string;
  channel_type: string;
  transaction_count: number;
  total_amount: number;
  mdr_rate: number;
  mdr_amount: number;
  vat_percentage: number;
  vat_amount: number;
  net_amount: number;
  withholding_tax_rate: number;
  withhold_tax: number;
  transfer_fee: number;
  reimbursement_fee: number;
  service_fee: number;
  final_net_amount: number;
  cup_business_tax_fee: number;
  is_transfer: number;
  create_by: string;
  create_dt: string;
  update_by: string;
  update_dt: string;
}



export function TransactionSummaryScreen() {
  const { user } = useAuth();
  const { showNotification } = useNotification();

  // State variables for new workflow
  const [todayTransactions, setTodayTransactions] = useState<TransactionSummaryDetailItem[]>([]);
  const [pendingTransactions, setPendingTransactions] = useState<TransactionSummaryDetailItem[]>([]);
  const [filteredTransactions, setFilteredTransactions] = useState<TransactionSummaryDetailItem[]>([]);

  // Loading states
  const [todayLoading, setTodayLoading] = useState(false);
  const [pendingLoading, setPendingLoading] = useState(false);
  const [filteredLoading, setFilteredLoading] = useState(false);

  // Filter states
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [endDate, setEndDate] = useState(new Date().toISOString().split('T')[0]);
  const [merchantVat, setMerchantVat] = useState('');
  const [channelType, setChannelType] = useState('');
  const [transferFilter, setTransferFilter] = useState<number | undefined>(undefined);

  // Active tab state
  const [activeTab, setActiveTab] = useState<'today' | 'pending' | 'filtered'>('today');
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);

  // Load today's transactions (current date)
  const loadTodayTransactions = async () => {
    setTodayLoading(true);
    try {
      const today = new Date().toISOString().split('T')[0];
      console.log('🔍 Loading today\'s transactions for:', today);

      const result = await safeIpcInvoke('get-transaction-summary-details', {
        startDate: today,
        endDate: today,
        page: 1,
        pageSize: 1000
      });

      if (result.success) {
        setTodayTransactions(result.data || []);
        console.log(`📋 Loaded ${result.data?.length || 0} today's transactions`);
      } else {
        showNotification('Failed to load today\'s transactions: ' + (result.error || 'Unknown error'), 'error');
        setTodayTransactions([]);
      }
    } catch (error) {
      console.error('Error loading today\'s transactions:', error);
      showNotification('Error loading today\'s transactions', 'error');
      setTodayTransactions([]);
    } finally {
      setTodayLoading(false);
      setLastRefresh(new Date());
    }
  };

  // Load all pending transactions (across all dates)
  const loadPendingTransactions = async () => {
    setPendingLoading(true);
    try {
      console.log('🔍 Loading all pending transactions...');

      const result = await safeIpcInvoke('get-transaction-summary-details', {
        isTransfer: 0, // Only pending transactions
        page: 1,
        pageSize: 1000
      });

      if (result.success) {
        setPendingTransactions(result.data || []);
        console.log(`📋 Loaded ${result.data?.length || 0} pending transactions`);
      } else {
        showNotification('Failed to load pending transactions: ' + (result.error || 'Unknown error'), 'error');
        setPendingTransactions([]);
      }
    } catch (error) {
      console.error('Error loading pending transactions:', error);
      showNotification('Error loading pending transactions', 'error');
      setPendingTransactions([]);
    } finally {
      setPendingLoading(false);
      setLastRefresh(new Date());
    }
  };

  // Load filtered transactions based on user criteria
  const loadFilteredTransactions = async () => {
    setFilteredLoading(true);
    try {
      console.log('🔍 Loading filtered transactions with criteria:', {
        startDate: selectedDate,
        endDate: endDate,
        merchantVat,
        channelType,
        transferFilter
      });

      const result = await safeIpcInvoke('get-transaction-summary-details', {
        startDate: selectedDate,
        endDate: endDate,
        merchantVat: merchantVat || undefined,
        channelType: channelType || undefined,
        isTransfer: transferFilter,
        page: 1,
        pageSize: 1000
      });

      if (result.success) {
        setFilteredTransactions(result.data || []);
        console.log(`📋 Loaded ${result.data?.length || 0} filtered transactions`);
      } else {
        showNotification('Failed to load filtered transactions: ' + (result.error || 'Unknown error'), 'error');
        setFilteredTransactions([]);
      }
    } catch (error) {
      console.error('Error loading filtered transactions:', error);
      showNotification('Error loading filtered transactions', 'error');
      setFilteredTransactions([]);
    } finally {
      setFilteredLoading(false);
      setLastRefresh(new Date());
    }
  };







  // Event handlers for new workflow
  const handleRefreshAll = async () => {
    try {
      await Promise.all([
        loadTodayTransactions(),
        loadPendingTransactions(),
        loadFilteredTransactions()
      ]);
      showNotification('All transaction data refreshed', 'success');
    } catch (error) {
      console.error('Error refreshing all data:', error);
      showNotification('Error refreshing data', 'error');
    }
  };

  const handleSearchFiltered = async () => {
    await loadFilteredTransactions();
    showNotification('Filtered transactions loaded', 'success');
  };

  const handleClearFilters = () => {
    setSelectedDate(new Date().toISOString().split('T')[0]);
    setEndDate(new Date().toISOString().split('T')[0]);
    setMerchantVat('');
    setChannelType('');
    setTransferFilter(undefined);
    showNotification('Filters cleared', 'info');
  };

  const updateTransferStatus = async (detailId: number, isTransfer: number) => {
    if (!user?.user_name) {
      showNotification('User not authenticated', 'error');
      return;
    }

    try {
      const result = await safeIpcInvoke('update-transfer-status', detailId, isTransfer, user.user_name);

      if (result.success) {
        showNotification(result.message, 'success');
        // Refresh all data to reflect changes
        await handleRefreshAll();
      } else {
        showNotification('Failed to update transfer status: ' + (result.error || 'Unknown error'), 'error');
      }
    } catch (error) {
      console.error('Error updating transfer status:', error);
      showNotification('Error updating transfer status', 'error');
    }
  };

  const handleExport = () => {
    let dataToExport: TransactionSummaryDetailItem[] = [];
    let filename = '';

    switch (activeTab) {
      case 'today':
        dataToExport = todayTransactions;
        filename = `transactions-today-${new Date().toISOString().split('T')[0]}.csv`;
        break;
      case 'pending':
        dataToExport = pendingTransactions;
        filename = `transactions-pending-${new Date().toISOString().split('T')[0]}.csv`;
        break;
      case 'filtered':
        dataToExport = filteredTransactions;
        filename = `transactions-filtered-${selectedDate}-to-${endDate}.csv`;
        break;
      default:
        dataToExport = [];
    }

    if (dataToExport.length === 0) {
      showNotification('No data to export', 'warning');
      return;
    }

    const csvData = dataToExport.map(item => ({
      'Transaction Date': item.transaction_date,
      'Merchant VAT': item.merchant_vat,
      'Merchant Name': item.merchant_name,
      'Channel Type': item.channel_type,
      'Transaction Count': item.transaction_count,
      'Total Amount': item.total_amount.toFixed(2),
      'MDR Amount': item.mdr_amount.toFixed(2),
      'VAT Amount': item.vat_amount.toFixed(2),
      'Final Net Amount': item.final_net_amount.toFixed(2),
      'Transfer Status': item.is_transfer === 1 ? 'Transferred' : 'Pending'
    }));

    const csvContent = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    URL.revokeObjectURL(url);

    showNotification('Data exported successfully', 'success');
  };

  const handlePrint = () => {
    window.print();
  };

  // Format currency safely
  const formatCurrency = (amount: number | null | undefined) => {
    if (amount === null || amount === undefined || isNaN(amount)) {
      return new Intl.NumberFormat('th-TH', {
        style: 'currency',
        currency: 'THB',
        minimumFractionDigits: 2
      }).format(0);
    }
    return new Intl.NumberFormat('th-TH', {
      style: 'currency',
      currency: 'THB',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Format date safely for display
  const formatDateTime = (date: Date | string | null | undefined) => {
    if (!date) return '';
    try {
      if (date instanceof Date) {
        return date.toLocaleTimeString();
      }
      if (typeof date === 'string') {
        return new Date(date).toLocaleTimeString();
      }
      return String(date);
    } catch (error) {
      console.error('Error formatting date:', error);
      return String(date);
    }
  };

  // Format date for table display (date only)
  const formatDate = (date: Date | string | null | undefined) => {
    if (!date) return '';
    try {
      if (date instanceof Date) {
        return date.toLocaleDateString();
      }
      if (typeof date === 'string') {
        // If it's already a string in YYYY-MM-DD format, return as is
        if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
          return date;
        }
        return new Date(date).toLocaleDateString();
      }
      return String(date);
    } catch (error) {
      console.error('Error formatting date:', error);
      return String(date);
    }
  };

  // Load initial data for all tabs
  useEffect(() => {
    const initializeData = async () => {
      console.log('🚀 Initializing transaction summary data...');

      try {
        // Load all data concurrently for the new workflow
        await Promise.all([
          loadTodayTransactions(),
          loadPendingTransactions(),
          loadFilteredTransactions()
        ]);

        console.log('✅ Initial data loading complete');
      } catch (error) {
        console.error('❌ Error during initialization:', error);
        showNotification('Error loading initial data', 'error');
      }
    };

    initializeData();
  }, []); // Empty dependency array for one-time initialization

  // Handle tab changes to ensure data is fresh
  useEffect(() => {
    if (activeTab === 'today' && todayTransactions.length === 0 && !todayLoading) {
      console.log('📋 Switching to today tab - loading data if empty');
      loadTodayTransactions();
    }
    if (activeTab === 'pending' && pendingTransactions.length === 0 && !pendingLoading) {
      console.log('📋 Switching to pending tab - loading data if empty');
      loadPendingTransactions();
    }
    if (activeTab === 'filtered' && filteredTransactions.length === 0 && !filteredLoading) {
      console.log('📋 Switching to filtered tab - loading data if empty');
      loadFilteredTransactions();
    }
  }, [activeTab]);

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-blue-600 text-white p-4 rounded-t-lg">
          <h1 className="text-xl font-bold">Transaction Summary</h1>
        </div>

        {/* Controls */}
        <div className="bg-white p-4 border-x border-gray-300">
          <div className="flex flex-wrap gap-4 items-center">
            <Button
              onClick={handleRefreshAll}
              disabled={todayLoading || pendingLoading || filteredLoading}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {(todayLoading || pendingLoading || filteredLoading) ? 'Refreshing...' : 'Refresh All'}
            </Button>
            <Button
              onClick={handleSearchFiltered}
              disabled={filteredLoading}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {filteredLoading ? 'Searching...' : 'Search Filtered'}
            </Button>
            <Button onClick={handleClearFilters} variant="secondary">
              Clear Filters
            </Button>
            <Button onClick={handleExport} variant="secondary">
              Export Current Tab
            </Button>
            <Button onClick={handlePrint} variant="secondary">
              Print
            </Button>
          </div>

          {/* Filters for Filtered Tab */}
          <div className="flex flex-wrap gap-4 items-center mt-4 p-3 bg-gray-50 rounded">
            <div className="text-sm font-semibold text-gray-700 mr-2">Filters (for "List Transaction Follow Filter" tab):</div>
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">Start Date:</label>
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="px-2 py-1 border border-gray-300 rounded text-sm"
              />
            </div>
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">End Date:</label>
              <input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="px-2 py-1 border border-gray-300 rounded text-sm"
              />
            </div>
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">Merchant VAT:</label>
              <input
                type="text"
                value={merchantVat}
                onChange={(e) => setMerchantVat(e.target.value)}
                placeholder="Enter VAT number"
                className="px-2 py-1 border border-gray-300 rounded text-sm w-40"
              />
            </div>
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">Channel:</label>
              <select
                value={channelType}
                onChange={(e) => setChannelType(e.target.value)}
                className="px-2 py-1 border border-gray-300 rounded text-sm"
              >
                <option value="">All Channels</option>
                <option value="WeChat">WeChat</option>
                <option value="UNIPAY">UNIPAY</option>
              </select>
            </div>
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">Transfer Status:</label>
              <select
                value={transferFilter === undefined ? '' : transferFilter.toString()}
                onChange={(e) => setTransferFilter(e.target.value === '' ? undefined : parseInt(e.target.value))}
                className="px-2 py-1 border border-gray-300 rounded text-sm"
              >
                <option value="">All</option>
                <option value="0">Pending</option>
                <option value="1">Transferred</option>
              </select>
            </div>
          </div>
        </div>

        {/* Summary Section */}
        <div className="bg-white p-4 border-x border-gray-300">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Today's Transactions */}
            <div className="bg-blue-100 p-3 rounded">
              <div className="text-sm text-blue-700 flex items-center gap-2">
                Today's Transactions
                {todayLoading && (
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent"></div>
                )}
              </div>
              <div className="text-lg font-bold text-blue-800">
                {todayTransactions.length} records
              </div>
              <div className="text-sm text-blue-600">
                Total: {formatCurrency(todayTransactions.reduce((sum, item) => sum + item.final_net_amount, 0))}
              </div>
            </div>

            {/* Pending Transactions */}
            <div className="bg-orange-100 p-3 rounded">
              <div className="text-sm text-orange-700 flex items-center gap-2">
                Pending Transactions
                {pendingLoading && (
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-orange-600 border-t-transparent"></div>
                )}
              </div>
              <div className="text-lg font-bold text-orange-800">
                {pendingTransactions.length} records
              </div>
              <div className="text-sm text-orange-600">
                Total: {formatCurrency(pendingTransactions.reduce((sum, item) => sum + item.final_net_amount, 0))}
              </div>
            </div>

            {/* Filtered Transactions */}
            <div className="bg-green-100 p-3 rounded">
              <div className="text-sm text-green-700 flex items-center gap-2">
                Filtered Transactions
                {filteredLoading && (
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-green-600 border-t-transparent"></div>
                )}
              </div>
              <div className="text-lg font-bold text-green-800">
                {filteredTransactions.length} records
              </div>
              <div className="text-sm text-green-600">
                Total: {formatCurrency(filteredTransactions.reduce((sum, item) => sum + item.final_net_amount, 0))}
              </div>
            </div>
          </div>

          {/* Last refresh info */}
          {lastRefresh && (
            <div className="mt-4 flex items-center gap-4">
              <span className="text-sm text-gray-500">
                Last updated: {formatDateTime(lastRefresh)}
              </span>
            </div>
          )}
        </div>

        {/* Tab Navigation */}
        <div className="bg-white border-x border-gray-300">
          <div className="flex border-b overflow-x-auto">
            <button
              onClick={() => setActiveTab('today')}
              className={`px-4 py-2 font-medium whitespace-nowrap ${
                activeTab === 'today'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <span className="flex items-center gap-2">
                📅 List Transaction Today
                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                  {todayTransactions.length}
                </span>
              </span>
            </button>
            <button
              onClick={() => setActiveTab('pending')}
              className={`px-4 py-2 font-medium whitespace-nowrap ${
                activeTab === 'pending'
                  ? 'border-b-2 border-orange-500 text-orange-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <span className="flex items-center gap-2">
                ⏳ List Transaction Pending
                <span className="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                  {pendingTransactions.length}
                </span>
              </span>
            </button>
            <button
              onClick={() => setActiveTab('filtered')}
              className={`px-4 py-2 font-medium whitespace-nowrap ${
                activeTab === 'filtered'
                  ? 'border-b-2 border-green-500 text-green-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <span className="flex items-center gap-2">
                🔍 List Transaction Follow Filter
                <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                  {filteredTransactions.length}
                </span>
              </span>
            </button>
          </div>
        </div>

        {/* Content Area */}
        <div className="bg-white border border-gray-300 rounded-b-lg overflow-hidden">
          {/* Today's Transactions Tab */}
          {activeTab === 'today' && (
            <div className="p-4">
              <div className="mb-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <h3 className="text-lg font-semibold text-blue-800">
                    Today's Transactions ({new Date().toLocaleDateString()})
                  </h3>
                  {todayLoading && (
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-blue-600 border-t-transparent"></div>
                  )}
                </div>
                <p className="text-sm text-blue-700">
                  Showing all transactions for today's date. Total records: <strong>{todayTransactions.length}</strong>
                </p>
                {lastRefresh && (
                  <p className="text-xs text-blue-600 mt-1">
                    Last updated: {formatDateTime(lastRefresh)}
                  </p>
                )}
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-blue-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        Merchant VAT
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        Merchant Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        Channel
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        Count
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        Total Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        Final Net Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        Transfer Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {todayLoading && todayTransactions.length === 0 && (
                      [...Array(5)].map((_, index) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-blue-50'}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-blue-200 h-4 w-20 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-blue-200 h-4 w-24 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-blue-200 h-4 w-32 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-blue-200 h-4 w-16 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-blue-200 h-4 w-12 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-blue-200 h-4 w-20 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-blue-200 h-4 w-20 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-blue-200 h-4 w-16 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-blue-200 h-4 w-16 rounded"></div>
                          </td>
                        </tr>
                      ))
                    )}
                    {todayTransactions.map((item, index) => (
                      <tr key={item.id} className={index % 2 === 0 ? 'bg-white' : 'bg-blue-50'}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatDate(item.transaction_date)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.merchant_vat}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.merchant_name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.channel_type}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.transaction_count}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(item.total_amount)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold text-blue-800">
                          {formatCurrency(item.final_net_amount)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            item.is_transfer === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {item.is_transfer === 1 ? 'Transferred' : 'Pending'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <RoleBasedComponent requiredPermission="canCreate">
                            {item.is_transfer === 0 ? (
                              <button
                                onClick={() => updateTransferStatus(item.id, 1)}
                                className="text-green-600 hover:text-green-900 bg-green-100 hover:bg-green-200 px-3 py-1 rounded text-xs font-medium"
                              >
                                Mark Transferred
                              </button>
                            ) : (
                              <button
                                onClick={() => updateTransferStatus(item.id, 0)}
                                className="text-red-600 hover:text-red-900 bg-red-100 hover:bg-red-200 px-3 py-1 rounded text-xs font-medium"
                              >
                                Mark Pending
                              </button>
                            )}
                          </RoleBasedComponent>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {todayTransactions.length === 0 && !todayLoading && (
                  <div className="text-center py-8 text-gray-500">
                    <div className="text-blue-600 text-lg mb-2">📅</div>
                    <div>No transactions found for today</div>
                  </div>
                )}
              </div>

              {/* Summary for today's transactions */}
              {todayTransactions.length > 0 && (
                <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="text-sm font-semibold text-blue-800 mb-2">Today's Summary</h4>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-blue-700">Total Records:</span>
                      <span className="font-semibold text-blue-900 ml-2">{todayTransactions.length}</span>
                    </div>
                    <div>
                      <span className="text-blue-700">Total Amount:</span>
                      <span className="font-semibold text-blue-900 ml-2">
                        {formatCurrency(todayTransactions.reduce((sum, item) => sum + item.final_net_amount, 0))}
                      </span>
                    </div>
                    <div>
                      <span className="text-blue-700">Transferred:</span>
                      <span className="font-semibold text-blue-900 ml-2">
                        {todayTransactions.filter(item => item.is_transfer === 1).length}
                      </span>
                    </div>
                    <div>
                      <span className="text-blue-700">Pending:</span>
                      <span className="font-semibold text-blue-900 ml-2">
                        {todayTransactions.filter(item => item.is_transfer === 0).length}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
          {/* Pending Transactions Tab */}
          {activeTab === 'pending' && (
            <div className="p-4">
              <div className="mb-4 bg-orange-50 border border-orange-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-orange-500 rounded-full animate-pulse"></div>
                  <h3 className="text-lg font-semibold text-orange-800">
                    All Pending Transactions
                  </h3>
                  {pendingLoading && (
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-orange-600 border-t-transparent"></div>
                  )}
                </div>
                <p className="text-sm text-orange-700">
                  Showing all pending transactions across <strong>all dates</strong>. Total records: <strong>{pendingTransactions.length}</strong>
                </p>
                {lastRefresh && (
                  <p className="text-xs text-orange-600 mt-1">
                    Last updated: {formatDateTime(lastRefresh)}
                  </p>
                )}
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-orange-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-orange-700 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-orange-700 uppercase tracking-wider">
                        Merchant VAT
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-orange-700 uppercase tracking-wider">
                        Merchant Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-orange-700 uppercase tracking-wider">
                        Channel
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-orange-700 uppercase tracking-wider">
                        Count
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-orange-700 uppercase tracking-wider">
                        Total Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-orange-700 uppercase tracking-wider">
                        Final Net Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-orange-700 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {pendingLoading && pendingTransactions.length === 0 && (
                      [...Array(5)].map((_, index) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-orange-50'}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-orange-200 h-4 w-20 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-orange-200 h-4 w-24 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-orange-200 h-4 w-32 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-orange-200 h-4 w-16 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-orange-200 h-4 w-12 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-orange-200 h-4 w-20 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-orange-200 h-4 w-20 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-orange-200 h-4 w-16 rounded"></div>
                          </td>
                        </tr>
                      ))
                    )}
                    {pendingTransactions.map((item, index) => (
                      <tr key={item.id} className={index % 2 === 0 ? 'bg-white' : 'bg-orange-50'}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatDate(item.transaction_date)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.merchant_vat}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.merchant_name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.channel_type}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.transaction_count}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(item.total_amount)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold text-orange-800">
                          {formatCurrency(item.final_net_amount)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <RoleBasedComponent requiredPermission="canCreate">
                            <button
                              onClick={() => updateTransferStatus(item.id, 1)}
                              className="text-green-600 hover:text-green-900 bg-green-100 hover:bg-green-200 px-3 py-1 rounded text-xs font-medium"
                            >
                              Mark Transferred
                            </button>
                          </RoleBasedComponent>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {pendingTransactions.length === 0 && !pendingLoading && (
                  <div className="text-center py-8 text-gray-500">
                    <div className="text-green-600 text-lg mb-2">🎉</div>
                    <div>No pending transactions found - All transfers are complete!</div>
                  </div>
                )}
              </div>

              {/* Summary for pending transactions */}
              {pendingTransactions.length > 0 && (
                <div className="mt-4 bg-orange-50 border border-orange-200 rounded-lg p-4">
                  <h4 className="text-sm font-semibold text-orange-800 mb-2">Pending Transactions Summary</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-orange-700">Total Records:</span>
                      <span className="font-semibold text-orange-900 ml-2">{pendingTransactions.length}</span>
                    </div>
                    <div>
                      <span className="text-orange-700">Total Amount:</span>
                      <span className="font-semibold text-orange-900 ml-2">
                        {formatCurrency(pendingTransactions.reduce((sum, item) => sum + item.final_net_amount, 0))}
                      </span>
                    </div>
                    <div>
                      <span className="text-orange-700">Unique Merchants:</span>
                      <span className="font-semibold text-orange-900 ml-2">
                        {new Set(pendingTransactions.map(item => item.merchant_vat)).size}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Filtered Transactions Tab */}
          {activeTab === 'filtered' && (
            <div className="p-4">
              <div className="mb-4 bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <h3 className="text-lg font-semibold text-green-800">
                    Filtered Transactions
                  </h3>
                  {filteredLoading && (
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-green-600 border-t-transparent"></div>
                  )}
                </div>
                <p className="text-sm text-green-700">
                  Showing transactions based on your filter criteria. Total records: <strong>{filteredTransactions.length}</strong>
                </p>
                <div className="text-xs text-green-600 mt-1">
                  Date Range: {selectedDate} to {endDate}
                  {merchantVat && ` | Merchant VAT: ${merchantVat}`}
                  {channelType && ` | Channel: ${channelType}`}
                  {transferFilter !== undefined && ` | Status: ${transferFilter === 1 ? 'Transferred' : 'Pending'}`}
                </div>
                {lastRefresh && (
                  <p className="text-xs text-green-600 mt-1">
                    Last updated: {formatDateTime(lastRefresh)}
                  </p>
                )}
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-green-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                        Merchant VAT
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                        Merchant Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                        Channel
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                        Count
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                        Total Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                        Final Net Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                        Transfer Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredLoading && filteredTransactions.length === 0 && (
                      [...Array(5)].map((_, index) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-green-50'}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-green-200 h-4 w-20 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-green-200 h-4 w-24 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-green-200 h-4 w-32 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-green-200 h-4 w-16 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-green-200 h-4 w-12 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-green-200 h-4 w-20 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-green-200 h-4 w-20 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-green-200 h-4 w-16 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-green-200 h-4 w-16 rounded"></div>
                          </td>
                        </tr>
                      ))
                    )}
                    {filteredTransactions.map((item, index) => (
                      <tr key={item.id} className={index % 2 === 0 ? 'bg-white' : 'bg-green-50'}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatDate(item.transaction_date)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.merchant_vat}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.merchant_name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.channel_type}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.transaction_count}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(item.total_amount)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold text-green-800">
                          {formatCurrency(item.final_net_amount)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            item.is_transfer === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {item.is_transfer === 1 ? 'Transferred' : 'Pending'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <RoleBasedComponent requiredPermission="canCreate">
                            {item.is_transfer === 0 ? (
                              <button
                                onClick={() => updateTransferStatus(item.id, 1)}
                                className="text-green-600 hover:text-green-900 bg-green-100 hover:bg-green-200 px-3 py-1 rounded text-xs font-medium"
                              >
                                Mark Transferred
                              </button>
                            ) : (
                              <button
                                onClick={() => updateTransferStatus(item.id, 0)}
                                className="text-red-600 hover:text-red-900 bg-red-100 hover:bg-red-200 px-3 py-1 rounded text-xs font-medium"
                              >
                                Mark Pending
                              </button>
                            )}
                          </RoleBasedComponent>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {filteredTransactions.length === 0 && !filteredLoading && (
                  <div className="text-center py-8 text-gray-500">
                    <div className="text-green-600 text-lg mb-2">🔍</div>
                    <div>No transactions found matching your filter criteria</div>
                    <div className="text-sm text-gray-400 mt-2">Try adjusting your filters and search again</div>
                  </div>
                )}
              </div>

              {/* Summary for filtered transactions */}
              {filteredTransactions.length > 0 && (
                <div className="mt-4 bg-green-50 border border-green-200 rounded-lg p-4">
                  <h4 className="text-sm font-semibold text-green-800 mb-2">Filtered Results Summary</h4>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-green-700">Total Records:</span>
                      <span className="font-semibold text-green-900 ml-2">{filteredTransactions.length}</span>
                    </div>
                    <div>
                      <span className="text-green-700">Total Amount:</span>
                      <span className="font-semibold text-green-900 ml-2">
                        {formatCurrency(filteredTransactions.reduce((sum, item) => sum + item.final_net_amount, 0))}
                      </span>
                    </div>
                    <div>
                      <span className="text-green-700">Transferred:</span>
                      <span className="font-semibold text-green-900 ml-2">
                        {filteredTransactions.filter(item => item.is_transfer === 1).length}
                      </span>
                    </div>
                    <div>
                      <span className="text-green-700">Pending:</span>
                      <span className="font-semibold text-green-900 ml-2">
                        {filteredTransactions.filter(item => item.is_transfer === 0).length}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

        </div>
      </div>
    </div>
  );
}

