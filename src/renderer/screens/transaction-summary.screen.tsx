import { useState, useEffect } from 'react';
import { Button } from '../components/button';
import { useAuth } from '../contexts/AuthContext';
import { useNotification } from '../contexts/NotificationContext';
import { safeIpcInvoke } from '../utils/electron';
import { RoleBasedComponent } from '../components/RoleBasedComponent';

// Types for Transaction Summary using only detail table
interface TransactionSummaryDetailItem {
  id: number;
  merchant_vat: string;
  merchant_name: string;
  transaction_date: string;
  channel_type: string;
  transaction_count: number;
  total_amount: number;
  mdr_rate: number;
  mdr_amount: number;
  vat_percentage: number;
  vat_amount: number;
  net_amount: number;
  withholding_tax_rate: number;
  withhold_tax: number;
  transfer_fee: number;
  reimbursement_fee: number;
  service_fee: number;
  final_net_amount: number;
  cup_business_tax_fee: number;
  is_transfer: number;
  create_by: string;
  create_dt: string;
  update_by: string;
  update_dt: string;
}

interface TransactionSummaryAggregates {
  totalTransferred: number;
  totalPending: number;
  totalAmount: number;
  grossTotalAmount: number;
  totalMdrAmount: number;
  totalVatAmount: number;
  totalWithholdTax: number;
  totalTransferFee: number;
  recordCount: number;
  merchantCount: number;
  channelCount: number;
  channelBreakdown: Array<{
    channel_type: string;
    record_count: number;
    total_amount: number;
    transferred_amount: number;
    pending_amount: number;
  }>;
}

export function TransactionSummaryScreen() {
  const { user } = useAuth();
  const { showNotification } = useNotification();
  
  // State variables
  const [detailData, setDetailData] = useState<TransactionSummaryDetailItem[]>([]);
  const [pendingTransfersData, setPendingTransfersData] = useState<TransactionSummaryDetailItem[]>([]);
  const [aggregates, setAggregates] = useState<TransactionSummaryAggregates | null>(null);
  const [loading, setLoading] = useState(false);
  const [aggregatesLoading, setAggregatesLoading] = useState(false);
  const [pendingTransfersLoading, setPendingTransfersLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [merchantVat, setMerchantVat] = useState('');
  const [channelType, setChannelType] = useState('');
  const [transferFilter, setTransferFilter] = useState<number | undefined>(undefined);
  const [activeTab, setActiveTab] = useState<'summary' | 'details' | 'transfer-status' | 'pending-always'>('summary');
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);

  // Load data functions
  const loadDetailData = async () => {
    setLoading(true);
    try {
      const result = await safeIpcInvoke('get-transaction-summary-details', {
        startDate: selectedDate,
        endDate: selectedDate,
        merchantVat: merchantVat || undefined,
        channelType: channelType || undefined,
        isTransfer: transferFilter,
        page: 1,
        pageSize: 100
      });

      if (result.success) {
        setDetailData(result.data || []);
      } else {
        showNotification('Failed to load transaction details: ' + (result.error || 'Unknown error'), 'error');
        setDetailData([]);
      }
    } catch (error) {
      console.error('Error loading transaction details:', error);
      showNotification('Error loading transaction details', 'error');
      setDetailData([]);
    } finally {
      setLoading(false);
    }
  };

  const loadAggregateData = async () => {
    setAggregatesLoading(true);
    try {
      const result = await safeIpcInvoke('get-transaction-summary-aggregates', {
        startDate: selectedDate,
        endDate: selectedDate,
        merchantVat: merchantVat || undefined,
        channelType: channelType || undefined
      });

      if (result.success) {
        setAggregates(result.data);
      } else {
        showNotification('Failed to load summary aggregates: ' + (result.error || 'Unknown error'), 'error');
        setAggregates(null);
      }
    } catch (error) {
      console.error('Error loading summary aggregates:', error);
      showNotification('Error loading summary aggregates', 'error');
      setAggregates(null);
    } finally {
      setAggregatesLoading(false);
    }
  };

  // Load pending transfers specifically (ignores filters)
  const loadPendingTransfers = async () => {
    setPendingTransfersLoading(true);
    try {
      const result = await safeIpcInvoke('get-transaction-summary-details', {
        startDate: selectedDate,
        endDate: selectedDate,
        merchantVat: merchantVat || undefined,
        channelType: channelType || undefined,
        isTransfer: 0, // Only pending transfers
        page: 1,
        pageSize: 1000
      });

      if (result.success) {
        // Update only pending data in aggregates
        const pendingAmount = result.data.reduce((sum: number, item: TransactionSummaryDetailItem) =>
          sum + item.final_net_amount, 0);

        if (aggregates) {
          const previousPending = aggregates.totalPending;
          setAggregates({
            ...aggregates,
            totalPending: pendingAmount
          });

          // Show notification if pending amount changed
          if (previousPending !== pendingAmount) {
            showNotification(
              `Pending transfers updated: ${formatCurrency(pendingAmount)}`,
              'info'
            );
          }
        }
      }
    } catch (error) {
      console.error('Error loading pending transfers:', error);
    } finally {
      setPendingTransfersLoading(false);
      setLastRefresh(new Date());
    }
  };

  // Load ALL pending transfers (ignores all filters including date)
  const loadAllPendingTransfers = async () => {
    setPendingTransfersLoading(true);
    try {
      console.log('🔍 Loading all pending transfers (ignoring ALL filters)...');
      const result = await safeIpcInvoke('get-transaction-summary-details', {
        isTransfer: 0, // Only pending transfers - NO other filters (no date, no merchant, no channel)
        page: 1,
        pageSize: 1000
      });

      console.log('📋 All pending transfers result:', result);

      if (result.success) {
        setPendingTransfersData(result.data || []);
        console.log(`📋 Loaded ${result.data?.length || 0} pending transfers (ignoring ALL filters)`);
        
        if (result.data && result.data.length > 0) {
          showNotification(`Found ${result.data.length} pending transfers across all dates`, 'info');
        }
      } else {
        console.error('❌ Failed to load pending transfers:', result.error);
        showNotification('Failed to load pending transfers: ' + (result.error || 'Unknown error'), 'error');
        setPendingTransfersData([]);
      }
    } catch (error) {
      console.error('❌ Error loading all pending transfers:', error);
      showNotification('Error loading pending transfers', 'error');
      setPendingTransfersData([]);
    } finally {
      setPendingTransfersLoading(false);
      setLastRefresh(new Date());
    }
  };

  // Event handlers
  const handleSearch = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadDetailData(),
        loadAggregateData(),
        loadAllPendingTransfers() // Always load all pending transfers
      ]);
      showNotification('Transaction summary data loaded', 'success');
    } catch (error) {
      console.error('Error in handleSearch:', error);
      showNotification('Error loading data', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setSelectedDate(new Date().toISOString().split('T')[0]);
    setMerchantVat('');
    setChannelType('');
    setTransferFilter(undefined);
    showNotification('Form reset to default values', 'info');
  };

  const handleApprove = async () => {
    if (!user?.user_name) {
      showNotification('User not authenticated', 'error');
      return;
    }

    try {
      const result = await safeIpcInvoke('approve-transaction-summary', {
        startDate: selectedDate,
        endDate: selectedDate,
        merchantVat: merchantVat || undefined,
        channelType: channelType || undefined
      }, user.user_name);

      if (result.success) {
        showNotification(result.message, 'success');
        await handleSearch(); // Reload data
      } else {
        showNotification('Failed to approve: ' + (result.error || 'Unknown error'), 'error');
      }
    } catch (error) {
      console.error('Error approving transaction summary:', error);
      showNotification('Error approving transaction summary', 'error');
    }
  };

  const updateTransferStatus = async (detailId: number, isTransfer: number) => {
    if (!user?.user_name) {
      showNotification('User not authenticated', 'error');
      return;
    }

    try {
      const result = await safeIpcInvoke('update-transfer-status', detailId, isTransfer, user.user_name);

      if (result.success) {
        showNotification(result.message, 'success');
        // Reload data to reflect changes
        await Promise.all([
          loadDetailData(),
          loadAggregateData(),
          loadAllPendingTransfers() // Always refresh all pending transfers
        ]);
      } else {
        showNotification('Failed to update transfer status: ' + (result.error || 'Unknown error'), 'error');
      }
    } catch (error) {
      console.error('Error updating transfer status:', error);
      showNotification('Error updating transfer status', 'error');
    }
  };

  const handleExport = () => {
    if (detailData.length === 0) {
      showNotification('No data to export', 'warning');
      return;
    }

    const csvData = detailData.map(item => ({
      'Merchant VAT': item.merchant_vat,
      'Merchant Name': item.merchant_name,
      'Transaction Date': item.transaction_date,
      'Channel Type': item.channel_type,
      'Transaction Count': item.transaction_count,
      'Total Amount': item.total_amount.toFixed(2),
      'MDR Amount': item.mdr_amount.toFixed(2),
      'VAT Amount': item.vat_amount.toFixed(2),
      'Final Net Amount': item.final_net_amount.toFixed(2),
      'Transfer Status': item.is_transfer === 1 ? 'Transferred' : 'Pending'
    }));

    const csvContent = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `transaction-summary-${selectedDate}.csv`;
    link.click();
    URL.revokeObjectURL(url);

    showNotification('Data exported successfully', 'success');
  };

  const handlePrint = () => {
    window.print();
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('th-TH', {
      style: 'currency',
      currency: 'THB',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Format date safely
  const formatDateTime = (date: Date | string | null | undefined) => {
    if (!date) return '';
    try {
      if (date instanceof Date) {
        return date.toLocaleTimeString();
      }
      if (typeof date === 'string') {
        return new Date(date).toLocaleTimeString();
      }
      return String(date);
    } catch (error) {
      console.error('Error formatting date:', error);
      return String(date);
    }
  };

  // Load initial data
  useEffect(() => {
    const initializeData = async () => {
      console.log('🚀 Initializing transaction summary data...');
      setLoading(true);
      
      try {
        // Load all data concurrently
        await Promise.all([
          loadDetailData(),
          loadAggregateData(),
          loadAllPendingTransfers()
        ]);
        
        console.log('✅ Initial data loading complete');
      } catch (error) {
        console.error('❌ Error during initialization:', error);
        showNotification('Error loading initial data', 'error');
      } finally {
        setLoading(false);
      }
    };

    initializeData();
  }, []); // Empty dependency array for one-time initialization

  // Handle tab changes to ensure data is fresh
  useEffect(() => {
    if (activeTab === 'pending-always' && pendingTransfersData.length === 0 && !pendingTransfersLoading) {
      console.log('📋 Switching to pending-always tab - loading data if empty');
      loadAllPendingTransfers();
    }
  }, [activeTab]);

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-blue-600 text-white p-4 rounded-t-lg">
          <h1 className="text-xl font-bold">Transaction Summary</h1>
        </div>

        {/* Controls */}
        <div className="bg-white p-4 border-x border-gray-300">
          <div className="flex flex-wrap gap-4 items-center">
            <Button onClick={handleSearch} disabled={loading}>
              {loading ? 'Loading...' : 'Search'}
            </Button>
            <Button onClick={handleCancel} variant="secondary">
              Cancel
            </Button>
            <RoleBasedComponent requiredPermission="canCreate">
              <Button onClick={handleApprove} className="bg-green-600 hover:bg-green-700 text-white">
                Approve
              </Button>
            </RoleBasedComponent>
            <Button onClick={handleExport} variant="secondary">
              Export
            </Button>
            <Button onClick={handlePrint} variant="secondary">
              Print
            </Button>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-4 items-center mt-4">
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">Date:</label>
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="px-2 py-1 border border-gray-300 rounded text-sm"
              />
            </div>
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">Merchant VAT:</label>
              <input
                type="text"
                value={merchantVat}
                onChange={(e) => setMerchantVat(e.target.value)}
                placeholder="Enter VAT number"
                className="px-2 py-1 border border-gray-300 rounded text-sm w-40"
              />
            </div>
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">Channel:</label>
              <select
                value={channelType}
                onChange={(e) => setChannelType(e.target.value)}
                className="px-2 py-1 border border-gray-300 rounded text-sm"
              >
                <option value="">All Channels</option>
                <option value="WeChat">WeChat</option>
                <option value="UNIPAY">UNIPAY</option>
              </select>
            </div>
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">Transfer Status:</label>
              <select
                value={transferFilter === undefined ? '' : transferFilter.toString()}
                onChange={(e) => setTransferFilter(e.target.value === '' ? undefined : parseInt(e.target.value))}
                className="px-2 py-1 border border-gray-300 rounded text-sm"
              >
                <option value="">All</option>
                <option value="0">Pending</option>
                <option value="1">Transferred</option>
              </select>
            </div>
          </div>
        </div>

        {/* Summary Section */}
        <div className="bg-white p-4 border-x border-gray-300">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Transferred Today */}
            <div className="bg-green-100 p-3 rounded">
              <div className="text-sm text-green-700">Transferred Today</div>
              {aggregatesLoading ? (
                <div className="text-lg font-bold text-green-800">
                  <div className="animate-pulse bg-green-200 h-6 w-24 rounded"></div>
                </div>
              ) : (
                <div className="text-lg font-bold text-green-800">
                  {formatCurrency(aggregates?.totalTransferred || 0)}
                </div>
              )}
            </div>

            {/* Pending Transfer without auto-refresh indicators */}
            <div className="bg-red-100 p-3 rounded relative">
              <div className="text-sm text-red-700 flex items-center gap-2">
                Pending Transfer
                {pendingTransfersLoading && (
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-red-600 border-t-transparent"></div>
                )}
              </div>
              {aggregatesLoading ? (
                <div className="text-lg font-bold text-red-800">
                  <div className="animate-pulse bg-red-200 h-6 w-24 rounded"></div>
                </div>
              ) : (
                <div className="text-lg font-bold text-red-800">
                  {formatCurrency(aggregates?.totalPending || 0)}
                </div>
              )}
            </div>

            {/* Total Amount */}
            <div className="bg-blue-100 p-3 rounded">
              <div className="text-sm text-blue-700">Total Amount</div>
              {aggregatesLoading ? (
                <div className="text-lg font-bold text-blue-800">
                  <div className="animate-pulse bg-blue-200 h-6 w-24 rounded"></div>
                </div>
              ) : (
                <div className="text-lg font-bold text-blue-800">
                  {formatCurrency(aggregates?.totalAmount || 0)}
                </div>
              )}
            </div>

            {/* Record Count */}
            <div className="bg-gray-100 p-3 rounded">
              <div className="text-sm text-gray-700">Record Count</div>
              {aggregatesLoading ? (
                <div className="text-lg font-bold text-gray-800">
                  <div className="animate-pulse bg-gray-200 h-6 w-20 rounded"></div>
                </div>
              ) : (
                <div className="text-lg font-bold text-gray-800">
                  {aggregates?.recordCount || 0} records
                </div>
              )}
            </div>
          </div>

          {/* Manual refresh controls only */}
          <div className="mt-4 flex items-center gap-4">
            <button
              onClick={() => {
                console.log('🔄 Manual refresh triggered');
                loadAllPendingTransfers();
                if (activeTab === 'summary' || activeTab === 'details') {
                  loadAggregateData();
                }
              }}
              disabled={pendingTransfersLoading}
              className="px-3 py-1 text-sm bg-blue-100 text-blue-700 hover:bg-blue-200 rounded disabled:opacity-50"
            >
              {pendingTransfersLoading ? 'Refreshing...' : 'Refresh Pending'}
            </button>
            {lastRefresh && (
              <span className="text-sm text-gray-500">
                Last updated: {formatDateTime(lastRefresh)}
              </span>
            )}
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white border-x border-gray-300">
          <div className="flex border-b">
            <button
              onClick={() => setActiveTab('summary')}
              className={`px-4 py-2 font-medium ${
                activeTab === 'summary'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Summary
            </button>
            <button
              onClick={() => setActiveTab('details')}
              className={`px-4 py-2 font-medium ${
                activeTab === 'details'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Details
            </button>
            <button
              onClick={() => setActiveTab('transfer-status')}
              className={`px-4 py-2 font-medium ${
                activeTab === 'transfer-status'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Transfer Status
            </button>
            <button
              onClick={() => setActiveTab('pending-always')}
              className={`px-4 py-2 font-medium ${
                activeTab === 'pending-always'
                  ? 'border-b-2 border-red-500 text-red-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <span className="flex items-center gap-2">
                All Pending
                <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                  {pendingTransfersData.length}
                </span>
                <span className="text-xs text-gray-500">(All Dates)</span>
              </span>
            </button>
          </div>
        </div>

        {/* Content Area */}
        <div className="bg-white border border-gray-300 rounded-b-lg overflow-hidden">
          {activeTab === 'summary' && (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Merchant VAT
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Merchant Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Channel
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Transaction Count
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Final Net Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Transfer Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {detailData.map((item, index) => (
                    <tr key={item.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.merchant_vat}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.merchant_name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.channel_type}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.transaction_count}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatCurrency(item.total_amount)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatCurrency(item.final_net_amount)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          item.is_transfer === 1
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {item.is_transfer === 1 ? 'Transferred' : 'Pending'}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {loading && (
                <tbody className="bg-white divide-y divide-gray-200">
                  {[...Array(5)].map((_, index) => (
                    <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="animate-pulse bg-gray-200 h-4 w-24 rounded"></div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="animate-pulse bg-gray-200 h-4 w-32 rounded"></div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="animate-pulse bg-gray-200 h-4 w-16 rounded"></div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="animate-pulse bg-gray-200 h-4 w-12 rounded"></div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="animate-pulse bg-gray-200 h-4 w-20 rounded"></div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="animate-pulse bg-gray-200 h-4 w-20 rounded"></div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="animate-pulse bg-gray-200 h-4 w-16 rounded"></div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              )}
              {detailData.length === 0 && !loading && (
                <div className="text-center py-8 text-gray-500">
                  No transaction summary data found
                </div>
              )}
            </div>
          )}

          {activeTab === 'details' && (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 text-sm">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Merchant VAT</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Name</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Channel</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Count</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">MDR</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">VAT</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Net Amount</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Transfer</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {detailData.map((item, index) => (
                    <tr key={item.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <td className="px-4 py-3 whitespace-nowrap text-sm">{item.transaction_date}</td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm">{item.merchant_vat}</td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm">{item.merchant_name}</td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm">{item.channel_type}</td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm">{item.transaction_count}</td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm">{formatCurrency(item.total_amount)}</td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm">{formatCurrency(item.mdr_amount)}</td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm">{formatCurrency(item.vat_amount)}</td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm">{formatCurrency(item.final_net_amount)}</td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          item.is_transfer === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {item.is_transfer === 1 ? 'Yes' : 'No'}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {activeTab === 'transfer-status' && (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Merchant</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {detailData.map((item, index) => (
                    <tr key={item.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{item.merchant_name}</div>
                        <div className="text-sm text-gray-500">{item.merchant_vat}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatCurrency(item.final_net_amount)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          item.is_transfer === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {item.is_transfer === 1 ? 'Transferred' : 'Pending'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <RoleBasedComponent requiredPermission="canCreate">
                          {item.is_transfer === 0 ? (
                            <button
                              onClick={() => updateTransferStatus(item.id, 1)}
                              className="text-green-600 hover:text-green-900 mr-2"
                            >
                              Mark Transferred
                            </button>
                          ) : (
                            <button
                              onClick={() => updateTransferStatus(item.id, 0)}
                              className="text-red-600 hover:text-red-900 mr-2"
                            >
                              Mark Pending
                            </button>
                          )}
                        </RoleBasedComponent>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {activeTab === 'pending-always' && (
            <div className="p-4">
              <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                  <h3 className="text-lg font-semibold text-red-800">
                    All Pending Transfers (Ignores ALL Filters)
                  </h3>
                  {pendingTransfersLoading && (
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-red-600 border-t-transparent"></div>
                  )}
                </div>
                <p className="text-sm text-red-700">
                  This list shows ALL pending transfers across <strong>all dates, merchants, and channels</strong>, completely ignoring current filter settings.
                  Total records: <strong>{pendingTransfersData.length}</strong>
                </p>
                {lastRefresh && (
                  <p className="text-xs text-red-600 mt-1">
                    Last updated: {formatDateTime(lastRefresh)}
                  </p>
                )}
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-red-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-red-700 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-red-700 uppercase tracking-wider">
                        Merchant VAT
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-red-700 uppercase tracking-wider">
                        Merchant Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-red-700 uppercase tracking-wider">
                        Channel
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-red-700 uppercase tracking-wider">
                        Count
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-red-700 uppercase tracking-wider">
                        Total Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-red-700 uppercase tracking-wider">
                        Final Net Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-red-700 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {pendingTransfersLoading && pendingTransfersData.length === 0 && (
                      [...Array(5)].map((_, index) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-red-50'}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-red-200 h-4 w-20 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-red-200 h-4 w-24 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-red-200 h-4 w-32 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-red-200 h-4 w-16 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-red-200 h-4 w-12 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-red-200 h-4 w-20 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-red-200 h-4 w-20 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-red-200 h-4 w-16 rounded"></div>
                          </td>
                        </tr>
                      ))
                    )}
                    {pendingTransfersData.map((item, index) => (
                      <tr key={item.id} className={index % 2 === 0 ? 'bg-white' : 'bg-red-50'}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.transaction_date}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.merchant_vat}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.merchant_name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.channel_type}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.transaction_count}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(item.total_amount)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold text-red-800">
                          {formatCurrency(item.final_net_amount)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <RoleBasedComponent requiredPermission="canCreate">
                            <button
                              onClick={() => updateTransferStatus(item.id, 1)}
                              className="text-green-600 hover:text-green-900 bg-green-100 hover:bg-green-200 px-3 py-1 rounded text-xs font-medium"
                            >
                              Mark Transferred
                            </button>
                          </RoleBasedComponent>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {pendingTransfersData.length === 0 && !pendingTransfersLoading && (
                  <div className="text-center py-8 text-gray-500">
                    <div className="text-green-600 text-lg mb-2">🎉</div>
                    <div>No pending transfers found - All transfers are complete!</div>
                  </div>
                )}
              </div>

              {/* Summary for pending transfers */}
              {pendingTransfersData.length > 0 && (
                <div className="mt-4 bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="text-sm font-semibold text-red-800 mb-2">Pending Transfers Summary</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-red-700">Total Records:</span>
                      <span className="font-semibold text-red-900 ml-2">{pendingTransfersData.length}</span>
                    </div>
                    <div>
                      <span className="text-red-700">Total Amount:</span>
                      <span className="font-semibold text-red-900 ml-2">
                        {formatCurrency(pendingTransfersData.reduce((sum, item) => sum + item.final_net_amount, 0))}
                      </span>
                    </div>
                    <div>
                      <span className="text-red-700">Unique Merchants:</span>
                      <span className="font-semibold text-red-900 ml-2">
                        {new Set(pendingTransfersData.map(item => item.merchant_vat)).size}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
